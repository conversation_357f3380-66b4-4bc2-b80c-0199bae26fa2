<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physics Test</title>
</head>
<body>
    <h1>Physics Test</h1>
    <div id="output"></div>
    
    <script type="module">
        const output = document.getElementById('output');
        
        try {
            output.innerHTML += '<p>Testing physics system...</p>';
            
            // Test CANNON.js import
            const CANNON = await import('cannon-es');
            output.innerHTML += '<p>✅ CANNON.js loaded</p>';
            
            // Test THREE.js import
            const THREE = await import('three');
            output.innerHTML += '<p>✅ THREE.js loaded</p>';
            
            // Test PhysicsWorld
            const { PhysicsWorld } = await import('./src/physics/PhysicsWorld.js');
            output.innerHTML += '<p>✅ PhysicsWorld loaded</p>';
            
            // Test creating physics world
            const physicsConfig = {
                gravity: -9.82,
                broadphase: 'naive',
                solver: {
                    iterations: 10,
                    tolerance: 1e-4
                },
                timeStep: 1/60,
                maxSubSteps: 3
            };
            
            const physics = new PhysicsWorld(physicsConfig);
            physics.init();
            output.innerHTML += '<p>✅ PhysicsWorld initialized</p>';
            
            // Test createPlane method
            const plane = physics.createPlane();
            output.innerHTML += '<p>✅ createPlane method works</p>';
            
            output.innerHTML += '<p>✅ All physics tests passed!</p>';
            
        } catch (error) {
            console.error('Error:', error);
            output.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            output.innerHTML += `<p>Stack: ${error.stack}</p>`;
        }
    </script>
</body>
</html>
