<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
</head>
<body>
    <h1>Simple Module Test</h1>
    <div id="output"></div>
    
    <script type="module">
        const output = document.getElementById('output');
        
        try {
            output.innerHTML += '<p>Starting tests...</p>';
            
            // Test basic imports
            const { GameConfig } = await import('./src/config/GameConfig.js');
            output.innerHTML += '<p>✅ GameConfig loaded</p>';
            
            const { TrackConfig } = await import('./src/config/TrackConfig.js');
            output.innerHTML += '<p>✅ TrackConfig loaded</p>';
            
            const { PlaygroundTrackConfig } = await import('./src/config/tracks/PlaygroundTrack.js');
            output.innerHTML += '<p>✅ PlaygroundTrackConfig loaded</p>';
            
            const { TrackSelection } = await import('./src/systems/TrackSelection.js');
            output.innerHTML += '<p>✅ TrackSelection loaded</p>';
            
            // Test TrackSelection
            const trackSelection = new TrackSelection();
            output.innerHTML += '<p>✅ TrackSelection instantiated</p>';
            
            const tracks = trackSelection.getAvailableTracks();
            output.innerHTML += `<p>✅ Found ${tracks.length} tracks</p>`;
            
            tracks.forEach(track => {
                output.innerHTML += `<p>- ${track.name}</p>`;
            });
            
            output.innerHTML += '<p>✅ All basic tests passed!</p>';
            
        } catch (error) {
            console.error('Error:', error);
            output.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            output.innerHTML += `<p>Stack: ${error.stack}</p>`;
        }
    </script>
</body>
</html>
