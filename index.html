<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRacer - Three.js Racing Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            color: white;
        }

        #gameCanvas {
            display: block;
            width: 100vw;
            height: 100vh;
        }

        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        #hud {
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #speedometer {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            text-align: center;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas"></canvas>
    
    <div id="ui">
        <div id="loading">
            <div>Loading WebRacer...</div>
            <div style="margin-top: 10px; font-size: 16px;">Initializing physics and track</div>
        </div>
        
        <div id="hud" class="hidden">
            <div>Lap: <span id="lapCount">1</span>/3</div>
            <div>Position: <span id="position">1</span>/1</div>
            <div>Best Lap: <span id="bestLap">--:--</span></div>
        </div>
        
        <div id="speedometer" class="hidden">
            <div><span id="speed">0</span> km/h</div>
            <div style="font-size: 16px;">Boost: <span id="boost">100</span>%</div>
        </div>
        
        <div id="controls" class="hidden">
            <div>WASD / Arrow Keys: Steer & Accelerate</div>
            <div>Space: Brake | Shift: Boost</div>
            <div>R: Reset Car | C: Change Camera</div>
            <div>T: Switch Track | 1: Playground | 2: Racing</div>
        </div>

        <div id="trackInfo" class="hidden" style="position: absolute; top: 20px; right: 20px; text-align: right;">
            <div>Track: <span id="currentTrack">Playground</span></div>
            <div style="font-size: 12px; opacity: 0.8;">Press T to switch tracks</div>
        </div>
    </div>

    <script type="module" src="src/main.js"></script>
</body>
</html>
