#!/bin/bash

# WebRacer Game Startup Script (.command file for macOS)
# This script can be double-clicked to start the WebRacer game

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🏎️  Starting WebRacer Game..."
echo "================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "   Visit: https://nodejs.org/"
    echo ""
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    echo ""
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Make sure this script is in the game directory."
    echo "   Current directory: $SCRIPT_DIR"
    echo ""
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies."
        echo ""
        echo "Press any key to exit..."
        read -n 1
        exit 1
    fi
    echo "✅ Dependencies installed successfully!"
    echo ""
fi

# Start the development server
echo "🚀 Starting development server..."
echo ""
echo "🎮 Game Controls:"
echo "   W/↑ - Accelerate"
echo "   S/↓ - Brake/Reverse"
echo "   A/← - Steer Left"
echo "   D/→ - Steer Right"
echo "   C   - Switch Camera"
echo "   R   - Reset Car"
echo ""
echo "🌐 The game will open automatically in your browser"
echo "📱 Network access available on your local network"
echo ""
echo "💡 To stop the server: Press Ctrl+C in this terminal"
echo "💡 To close this window: Close the terminal after stopping the server"
echo "================================"
echo ""

# Function to open browser after a delay
open_browser() {
    sleep 3
    if command -v open &> /dev/null; then
        # macOS
        open "http://localhost:3000" 2>/dev/null || open "http://localhost:3001" 2>/dev/null
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open "http://localhost:3000" 2>/dev/null || xdg-open "http://localhost:3001" 2>/dev/null
    elif command -v start &> /dev/null; then
        # Windows (if running in Git Bash or similar)
        start "http://localhost:3000" 2>/dev/null || start "http://localhost:3001" 2>/dev/null
    fi
}

# Start browser opening in background
open_browser &

# Start the Vite development server
npm run dev

# Keep terminal open after server stops
echo ""
echo "🛑 Game server stopped."
echo ""
echo "Press any key to close this window..."
read -n 1
