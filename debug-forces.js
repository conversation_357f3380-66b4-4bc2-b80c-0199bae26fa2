import { CarConfig } from './src/config/CarConfig.js';

// Simulate the force calculations
function calculateEngineForce(throttle, config) {
    let force = 0;
    
    if (Math.abs(throttle) > 0.01) {
        // Calculate base engine force
        force = throttle * config.engine.acceleration.force;
        
        // Apply efficiency
        force *= config.engine.acceleration.efficiency;
        
        // Increase force when grounded for better traction
        force *= 1.2; // Assuming grounded
    }
    
    return force;
}

function calculateDragForce(speed, config) {
    if (speed < 5) {
        // Rolling resistance at low speeds
        const rollingResistance = config.tires.friction.rollResistance * config.physics.mass * 30; // gravity = -30
        return rollingResistance;
    }
    
    // Aerodynamic drag
    const dragMagnitude = 0.5 * config.aerodynamics.airDensity * config.aerodynamics.dragCoefficient * 
                         config.aerodynamics.frontalArea * speed * speed * 0.1; // Reduced by 90%
    
    return dragMagnitude;
}

function calculateAcceleration(engineForce, dragForce, mass) {
    const netForce = engineForce - dragForce;
    return netForce / mass;
}

console.log('🏎️ Car Force Analysis');
console.log('===================');
console.log();

console.log('Car Configuration:');
console.log(`Mass: ${CarConfig.physics.mass} kg`);
console.log(`Engine Force: ${CarConfig.engine.acceleration.force} N`);
console.log(`Engine Efficiency: ${CarConfig.engine.acceleration.efficiency}`);
console.log(`Rolling Resistance: ${CarConfig.tires.friction.rollResistance}`);
console.log(`Drag Coefficient: ${CarConfig.aerodynamics.dragCoefficient}`);
console.log();

console.log('Force Analysis at Different Speeds:');
console.log('===================================');

const throttle = 1.0; // Full throttle
const speeds = [0, 1, 2, 5, 10, 20, 50];

for (const speed of speeds) {
    const engineForce = calculateEngineForce(throttle, CarConfig);
    const dragForce = calculateDragForce(speed, CarConfig);
    const netForce = engineForce - dragForce;
    const acceleration = calculateAcceleration(engineForce, dragForce, CarConfig.physics.mass);
    
    console.log(`Speed: ${speed.toString().padStart(2)} m/s`);
    console.log(`  Engine Force: ${engineForce.toFixed(0).padStart(6)} N`);
    console.log(`  Drag Force:   ${dragForce.toFixed(0).padStart(6)} N`);
    console.log(`  Net Force:    ${netForce.toFixed(0).padStart(6)} N`);
    console.log(`  Acceleration: ${acceleration.toFixed(2).padStart(6)} m/s²`);
    console.log();
}

console.log('Expected Results:');
console.log('================');
console.log('At 0 m/s (stationary):');
console.log(`  Should have strong positive acceleration: ${calculateAcceleration(calculateEngineForce(1.0, CarConfig), calculateDragForce(0, CarConfig), CarConfig.physics.mass).toFixed(2)} m/s²`);
console.log();
console.log('At 1 m/s (3.6 km/h):');
console.log(`  Should still accelerate well: ${calculateAcceleration(calculateEngineForce(1.0, CarConfig), calculateDragForce(1, CarConfig), CarConfig.physics.mass).toFixed(2)} m/s²`);
console.log();

// Calculate theoretical top speed where engine force equals drag force
let topSpeed = 0;
for (let speed = 0; speed < 100; speed += 0.1) {
    const engineForce = calculateEngineForce(1.0, CarConfig);
    const dragForce = calculateDragForce(speed, CarConfig);
    if (dragForce >= engineForce) {
        topSpeed = speed;
        break;
    }
}

console.log(`Theoretical top speed: ${topSpeed.toFixed(1)} m/s (${(topSpeed * 3.6).toFixed(1)} km/h)`);
