<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Selection Test</title>
</head>
<body>
    <h1>Track Selection Test</h1>
    <div id="output"></div>
    
    <script type="module">
        import { TrackSelection } from './src/systems/TrackSelection.js';
        
        const output = document.getElementById('output');
        
        try {
            console.log('Testing TrackSelection...');
            const trackSelection = new TrackSelection();
            
            output.innerHTML += '<p>✅ TrackSelection created successfully</p>';
            
            const tracks = trackSelection.getAvailableTracks();
            output.innerHTML += `<p>Available tracks: ${tracks.length}</p>`;
            
            tracks.forEach(track => {
                output.innerHTML += `<p>- ${track.name}: ${track.description}</p>`;
            });
            
            const currentTrack = trackSelection.getCurrentTrack();
            output.innerHTML += `<p>Current track: ${currentTrack.name}</p>`;
            
            // Test switching tracks
            trackSelection.setCurrentTrack('racing');
            const newTrack = trackSelection.getCurrentTrack();
            output.innerHTML += `<p>Switched to: ${newTrack.name}</p>`;
            
            output.innerHTML += '<p>✅ All tests passed!</p>';
            
        } catch (error) {
            console.error('Error:', error);
            output.innerHTML += `<p>❌ Error: ${error.message}</p>`;
        }
    </script>
</body>
</html>
