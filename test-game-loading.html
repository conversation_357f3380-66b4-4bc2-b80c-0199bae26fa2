<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Loading Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Game Loading Test</h1>
    <div id="output"></div>
    
    <script type="module">
        const output = document.getElementById('output');
        
        function addOutput(message, type = 'info') {
            const p = document.createElement('p');
            p.className = type;
            p.innerHTML = message;
            output.appendChild(p);
        }
        
        async function testGameLoading() {
            try {
                addOutput('<strong>🎮 Testing Game Loading...</strong>');
                
                // Test 1: Load all dependencies
                addOutput('<strong>Step 1: Loading Dependencies</strong>');
                const THREE = await import('three');
                addOutput('✅ THREE.js loaded', 'success');
                
                const CANNON = await import('cannon-es');
                addOutput('✅ CANNON.js loaded', 'success');
                
                // Test 2: Load configurations
                addOutput('<strong>Step 2: Loading Configurations</strong>');
                const { GameConfig } = await import('./src/config/GameConfig.js');
                addOutput('✅ GameConfig loaded', 'success');
                
                const { PlaygroundTrackConfig } = await import('./src/config/tracks/PlaygroundTrack.js');
                addOutput('✅ PlaygroundTrackConfig loaded', 'success');
                
                const { TrackConfig } = await import('./src/config/TrackConfig.js');
                addOutput('✅ TrackConfig loaded', 'success');
                
                // Test 3: Load core systems
                addOutput('<strong>Step 3: Loading Core Systems</strong>');
                const { TrackSelection } = await import('./src/systems/TrackSelection.js');
                addOutput('✅ TrackSelection loaded', 'success');
                
                const { PhysicsWorld } = await import('./src/physics/PhysicsWorld.js');
                addOutput('✅ PhysicsWorld loaded', 'success');
                
                const { Track } = await import('./src/entities/Track.js');
                addOutput('✅ Track loaded', 'success');
                
                // Test 4: Test track creation
                addOutput('<strong>Step 4: Testing Track Creation</strong>');
                
                // Test playground track
                try {
                    const playgroundTrack = new Track(PlaygroundTrackConfig);
                    addOutput('✅ Playground track instance created', 'success');
                    
                    // Test materials creation
                    playgroundTrack.createMaterials();
                    addOutput('✅ Playground track materials created', 'success');
                } catch (error) {
                    addOutput(`❌ Playground track error: ${error.message}`, 'error');
                }
                
                // Test racing track
                try {
                    const racingTrack = new Track(TrackConfig);
                    addOutput('✅ Racing track instance created', 'success');
                    
                    // Test materials creation
                    racingTrack.createMaterials();
                    addOutput('✅ Racing track materials created', 'success');
                } catch (error) {
                    addOutput(`❌ Racing track error: ${error.message}`, 'error');
                }
                
                // Test 5: Test track selection
                addOutput('<strong>Step 5: Testing Track Selection</strong>');
                const trackSelection = new TrackSelection();
                addOutput('✅ TrackSelection instantiated', 'success');
                
                const tracks = trackSelection.getAvailableTracks();
                addOutput(`✅ Found ${tracks.length} tracks`, 'success');
                
                // Test 6: Load main game class
                addOutput('<strong>Step 6: Loading Game Class</strong>');
                const { Game } = await import('./src/core/Game.js');
                addOutput('✅ Game class loaded', 'success');
                
                addOutput('<strong>🎉 All tests passed! The game should load without errors.</strong>', 'success');
                addOutput('<strong>You can now safely open the main game page.</strong>', 'info');
                
            } catch (error) {
                console.error('Error:', error);
                addOutput(`❌ Critical Error: ${error.message}`, 'error');
                addOutput(`Stack: ${error.stack}`, 'error');
                addOutput('<strong>⚠️ The game will not load properly until this error is fixed.</strong>', 'warning');
            }
        }
        
        testGameLoading();
    </script>
</body>
</html>
