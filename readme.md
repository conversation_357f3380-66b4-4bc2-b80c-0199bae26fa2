# 🏎️ WebRacer - 3D Racing Game

A fast-paced 3D racing game built with Three.js and Cannon.js physics, inspired by Wipeout-style gameplay.

## 🚀 Quick Start

### Option 1: <PERSON><PERSON> (macOS/Linux)
```bash
./start-game.sh
```

### Option 2: <PERSON><PERSON> (Windows)
```cmd
start-game.bat
```

### Option 3: Node.js <PERSON>ript (Cross-platform)
```bash
node start-game.js
# or
./start-game.js
```

### Option 5: Manual Start
```bash
npm install
npm run dev
```

## 🎮 Game Controls

| Key | Action |
|-----|--------|
| **W** or **↑** | Accelerate |
| **S** or **↓** | Brake/Reverse |
| **A** or **←** | Steer Left |
| **D** or **→** | Steer Right |
| **C** | Switch Camera |
| **R** | Reset Car |

## 🌐 Access the Game

Once started, the game will be available at:
- **Local**: http://localhost:3000
- **Network**: Available on your local network (IP shown in terminal)

## 📋 Requirements

- **Node.js** (v16 or higher)
- **npm** (comes with Node.js)
- Modern web browser with WebGL support

## 🛠️ Development

The game uses:
- **Three.js** for 3D graphics
- **Cannon.js** for physics simulation
- **Vite** for development server
- **Playwright** for testing

### Run Tests
```bash
npm test
```

### Build for Production
```bash
npm run build
```

## 🏁 Game Features

- ✅ **Realistic Physics**: Powered by Cannon.js
- ✅ **3D Graphics**: Beautiful Three.js rendering
- ✅ **Responsive Controls**: Smooth WASD/arrow key input
- ✅ **Multiple Cameras**: Switch between different views
- ✅ **Track Generation**: Procedural track creation
- ✅ **Car Physics**: Realistic acceleration, braking, and steering
- ✅ **Visual Effects**: Particles, lighting, and shadows

## 🐛 Troubleshooting

### Game Won't Start
1. Make sure Node.js is installed: `node --version`
2. Install dependencies: `npm install`
3. Try running manually: `npm run dev`

### Car Not Moving
The car movement has been fixed! Make sure you're:
- Holding down the keys (W/↑) for acceleration
- Using the correct controls listed above
- The car should respond immediately to input

### Performance Issues
- Close other browser tabs
- Reduce browser zoom level
- Check browser console for errors

## 📝 Recent Fixes

- ✅ Fixed car movement issues
- ✅ Resolved physics world initialization
- ✅ Corrected engine force direction
- ✅ Optimized suspension system
- ✅ Reduced friction for better movement
- ✅ Added comprehensive testing

## 🎯 Next Steps

- Add more tracks
- Implement lap timing
- Add sound effects
- Create multiplayer support
- Add car customization

---

**Enjoy racing! 🏁**