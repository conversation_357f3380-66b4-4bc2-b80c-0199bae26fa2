import * as THREE from 'three';

/**
 * Camera system with multiple camera modes for racing
 * Provides smooth camera following and transitions
 */
export class CameraSystem {
    constructor(config) {
        this.config = config;
        this.camera = null;
        this.target = null;
        
        // Camera modes
        this.modes = {
            FOLLOW: 'follow',
            CHASE: 'chase',
            COCKPIT: 'cockpit',
            OVERHEAD: 'overhead'
        };
        
        this.currentMode = this.modes.FOLLOW;
        
        // Camera state
        this.state = {
            position: new THREE.Vector3(),
            lookAt: new THREE.Vector3(),
            targetPosition: new THREE.Vector3(),
            targetLookAt: new THREE.Vector3(),
            velocity: new THREE.Vector3(),
            
            // Smoothing
            positionSmoothness: 0.1,
            lookAtSmoothness: 0.1,
            
            // Shake effect
            shake: {
                intensity: 0,
                decay: 0.95,
                offset: new THREE.Vector3()
            }
        };
        
        // Mode-specific settings
        this.modeSettings = {
            [this.modes.FOLLOW]: {
                distance: this.config.follow.distance,
                height: this.config.follow.height,
                lookAhead: this.config.follow.lookAhead,
                smoothness: this.config.follow.smoothness,
                rotationSmoothness: this.config.follow.rotationSmoothness
            },
            [this.modes.CHASE]: {
                distance: this.config.chase.distance,
                height: this.config.chase.height,
                lookAhead: this.config.chase.lookAhead,
                smoothness: this.config.chase.smoothness,
                rotationSmoothness: this.config.chase.rotationSmoothness
            },
            [this.modes.COCKPIT]: {
                distance: 0,
                height: 1.2,
                lookAhead: 10,
                smoothness: 0.05,
                rotationSmoothness: 0.02
            },
            [this.modes.OVERHEAD]: {
                distance: 0,
                height: 25,
                lookAhead: 0,
                smoothness: 0.08,
                rotationSmoothness: 0.1
            }
        };
        
        // Temporary vectors for calculations
        this.tempVec1 = new THREE.Vector3();
        this.tempVec2 = new THREE.Vector3();
        this.tempVec3 = new THREE.Vector3();
    }

    /**
     * Initialize camera system
     */
    init(scene) {
        // Create perspective camera
        this.camera = new THREE.PerspectiveCamera(
            this.config.fov,
            window.innerWidth / window.innerHeight,
            this.config.near,
            this.config.far
        );
        
        // Set initial position
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);
        
        // Add to scene
        scene.add(this.camera);
        
        // Store reference for skybox updates
        scene.userData.camera = this.camera;
        
        console.log('📷 Camera system initialized');
    }

    /**
     * Set the target object to follow
     */
    setTarget(target) {
        this.target = target;
        
        if (target) {
            // Initialize camera position relative to target
            this.state.position.copy(target.position);
            this.state.position.y += 5;
            this.state.position.z += 10;
            
            this.state.lookAt.copy(target.position);
        }
    }

    /**
     * Update camera position and orientation
     */
    update(deltaTime) {
        if (!this.target) return;
        
        // Calculate target camera position and look-at based on current mode
        this.calculateTargetTransform();
        
        // Apply smoothing
        this.applySmoothingToTransform(deltaTime);
        
        // Apply camera shake
        this.applyCameraShake(deltaTime);
        
        // Update camera transform
        this.updateCameraTransform();
    }

    /**
     * Calculate target camera position and look-at point
     */
    calculateTargetTransform() {
        const settings = this.modeSettings[this.currentMode];
        const targetPos = this.target.position;
        const targetRotation = this.target.quaternion;
        
        switch (this.currentMode) {
            case this.modes.FOLLOW:
                this.calculateFollowCamera(settings, targetPos, targetRotation);
                break;
                
            case this.modes.CHASE:
                this.calculateChaseCamera(settings, targetPos, targetRotation);
                break;
                
            case this.modes.COCKPIT:
                this.calculateCockpitCamera(settings, targetPos, targetRotation);
                break;
                
            case this.modes.OVERHEAD:
                this.calculateOverheadCamera(settings, targetPos, targetRotation);
                break;
        }
    }

    /**
     * Calculate follow camera position (behind and above target)
     */
    calculateFollowCamera(settings, targetPos, targetRotation) {
        // Get target's forward and up vectors
        const forward = new THREE.Vector3(0, 0, 1);
        const up = new THREE.Vector3(0, 1, 0);
        const right = new THREE.Vector3(1, 0, 0);

        forward.applyQuaternion(targetRotation);
        up.applyQuaternion(targetRotation);
        right.applyQuaternion(targetRotation);

        // Calculate camera position behind target
        this.state.targetPosition.copy(targetPos);
        this.state.targetPosition.addScaledVector(forward, -settings.distance);
        this.state.targetPosition.addScaledVector(up, settings.height);

        // Calculate look-at point ahead of target
        this.state.targetLookAt.copy(targetPos);
        this.state.targetLookAt.addScaledVector(forward, settings.lookAhead);

        // Update smoothness values
        this.state.positionSmoothness = settings.smoothness;
        this.state.lookAtSmoothness = settings.rotationSmoothness;
    }

    /**
     * Calculate chase camera position (further behind for dramatic effect)
     */
    calculateChaseCamera(settings, targetPos, targetRotation) {
        // Similar to follow but with different parameters
        this.calculateFollowCamera(settings, targetPos, targetRotation);
        
        // Add some side offset for more dynamic view
        const right = new THREE.Vector3(1, 0, 0);
        right.applyQuaternion(targetRotation);
        
        // Slight right offset
        this.state.targetPosition.addScaledVector(right, 1);
    }

    /**
     * Calculate cockpit camera position (inside the car)
     */
    calculateCockpitCamera(settings, targetPos, targetRotation) {
        const forward = new THREE.Vector3(0, 0, 1);
        const up = new THREE.Vector3(0, 1, 0);
        
        forward.applyQuaternion(targetRotation);
        up.applyQuaternion(targetRotation);
        
        // Position camera inside car
        this.state.targetPosition.copy(targetPos);
        this.state.targetPosition.addScaledVector(up, settings.height);
        
        // Look ahead
        this.state.targetLookAt.copy(targetPos);
        this.state.targetLookAt.addScaledVector(forward, settings.lookAhead);
        this.state.targetLookAt.addScaledVector(up, settings.height);
        
        this.state.positionSmoothness = settings.smoothness;
        this.state.lookAtSmoothness = settings.rotationSmoothness;
    }

    /**
     * Calculate overhead camera position (top-down view)
     */
    calculateOverheadCamera(settings, targetPos, targetRotation) {
        // Position camera directly above target
        this.state.targetPosition.copy(targetPos);
        this.state.targetPosition.y += settings.height;
        
        // Look down at target
        this.state.targetLookAt.copy(targetPos);
        
        this.state.positionSmoothness = settings.smoothness;
        this.state.lookAtSmoothness = settings.rotationSmoothness;
    }

    /**
     * Apply smoothing to camera transform
     */
    applySmoothingToTransform(deltaTime) {
        // Smooth position
        this.state.position.lerp(this.state.targetPosition, this.state.positionSmoothness);
        
        // Smooth look-at
        this.state.lookAt.lerp(this.state.targetLookAt, this.state.lookAtSmoothness);
    }

    /**
     * Apply camera shake effect
     */
    applyCameraShake(deltaTime) {
        if (this.state.shake.intensity > 0.01) {
            // Generate random shake offset
            this.state.shake.offset.set(
                (Math.random() - 0.5) * this.state.shake.intensity,
                (Math.random() - 0.5) * this.state.shake.intensity,
                (Math.random() - 0.5) * this.state.shake.intensity
            );
            
            // Decay shake intensity
            this.state.shake.intensity *= this.state.shake.decay;
        } else {
            this.state.shake.offset.set(0, 0, 0);
            this.state.shake.intensity = 0;
        }
    }

    /**
     * Update camera transform
     */
    updateCameraTransform() {
        // Apply position with shake
        this.tempVec1.copy(this.state.position);
        this.tempVec1.add(this.state.shake.offset);
        this.camera.position.copy(this.tempVec1);
        
        // Apply look-at with shake
        this.tempVec2.copy(this.state.lookAt);
        this.tempVec2.add(this.state.shake.offset);
        this.camera.lookAt(this.tempVec2);
    }

    /**
     * Switch camera mode
     */
    switchMode() {
        const modes = Object.values(this.modes);
        const currentIndex = modes.indexOf(this.currentMode);
        const nextIndex = (currentIndex + 1) % modes.length;
        
        this.currentMode = modes[nextIndex];
        console.log(`📷 Camera mode: ${this.currentMode}`);
    }

    /**
     * Set specific camera mode
     */
    setMode(mode) {
        if (this.modes[mode.toUpperCase()]) {
            this.currentMode = this.modes[mode.toUpperCase()];
            console.log(`📷 Camera mode: ${this.currentMode}`);
        }
    }

    /**
     * Add camera shake effect
     */
    addShake(intensity) {
        this.state.shake.intensity = Math.max(this.state.shake.intensity, intensity);
    }

    /**
     * Handle window resize
     */
    onWindowResize() {
        if (this.camera) {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
        }
    }

    /**
     * Get camera for rendering
     */
    getCamera() {
        return this.camera;
    }

    /**
     * Get current camera mode
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * Get camera position
     */
    getPosition() {
        return this.camera.position.clone();
    }

    /**
     * Get camera direction
     */
    getDirection() {
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);
        return direction;
    }
}
