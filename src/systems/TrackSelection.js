import { TrackConfig } from '../config/TrackConfig.js';
import { PlaygroundTrackConfig } from '../config/tracks/PlaygroundTrack.js';

/**
 * Track Selection System
 * Manages available tracks and track selection
 */
export class TrackSelection {
    constructor() {
        // Available tracks
        this.tracks = new Map();
        this.currentTrackId = 'playground'; // Default to playground
        
        // Initialize available tracks
        this.initializeTracks();
    }

    /**
     * Initialize available tracks
     */
    initializeTracks() {
        // Add playground track
        this.tracks.set('playground', {
            id: 'playground',
            name: 'Playground',
            description: 'Open flat area for testing and free driving',
            difficulty: 'beginner',
            type: 'playground',
            config: PlaygroundTrackConfig,
            thumbnail: null, // Could add thumbnail images later
            unlocked: true   // Always available
        });

        // Add original racing track
        this.tracks.set('racing', {
            id: 'racing',
            name: 'Racing Circuit',
            description: 'Fast-paced racing circuit with curves and elevation',
            difficulty: 'intermediate',
            type: 'racing',
            config: TrackConfig,
            thumbnail: null,
            unlocked: true
        });

        console.log(`🏁 Initialized ${this.tracks.size} tracks`);
    }

    /**
     * Get all available tracks
     */
    getAvailableTracks() {
        return Array.from(this.tracks.values()).filter(track => track.unlocked);
    }

    /**
     * Get track by ID
     */
    getTrack(trackId) {
        return this.tracks.get(trackId);
    }

    /**
     * Get current track
     */
    getCurrentTrack() {
        return this.tracks.get(this.currentTrackId);
    }

    /**
     * Set current track
     */
    setCurrentTrack(trackId) {
        if (this.tracks.has(trackId)) {
            const track = this.tracks.get(trackId);
            if (track.unlocked) {
                this.currentTrackId = trackId;
                console.log(`🏁 Selected track: ${track.name}`);
                return true;
            } else {
                console.warn(`🔒 Track "${track.name}" is locked`);
                return false;
            }
        } else {
            console.error(`❌ Track "${trackId}" not found`);
            return false;
        }
    }

    /**
     * Get current track configuration
     */
    getCurrentTrackConfig() {
        const currentTrack = this.getCurrentTrack();
        return currentTrack ? currentTrack.config : null;
    }

    /**
     * Unlock track
     */
    unlockTrack(trackId) {
        if (this.tracks.has(trackId)) {
            this.tracks.get(trackId).unlocked = true;
            console.log(`🔓 Unlocked track: ${this.tracks.get(trackId).name}`);
            return true;
        }
        return false;
    }

    /**
     * Lock track
     */
    lockTrack(trackId) {
        if (this.tracks.has(trackId)) {
            this.tracks.get(trackId).unlocked = false;
            console.log(`🔒 Locked track: ${this.tracks.get(trackId).name}`);
            return true;
        }
        return false;
    }

    /**
     * Add custom track
     */
    addTrack(trackData) {
        if (!trackData.id || !trackData.config) {
            console.error('❌ Invalid track data');
            return false;
        }

        this.tracks.set(trackData.id, {
            id: trackData.id,
            name: trackData.name || trackData.id,
            description: trackData.description || 'Custom track',
            difficulty: trackData.difficulty || 'custom',
            type: trackData.type || 'custom',
            config: trackData.config,
            thumbnail: trackData.thumbnail || null,
            unlocked: trackData.unlocked !== false
        });

        console.log(`➕ Added track: ${trackData.name || trackData.id}`);
        return true;
    }

    /**
     * Remove track
     */
    removeTrack(trackId) {
        if (this.tracks.has(trackId)) {
            const track = this.tracks.get(trackId);
            this.tracks.delete(trackId);
            
            // If removing current track, switch to playground
            if (this.currentTrackId === trackId) {
                this.currentTrackId = 'playground';
            }
            
            console.log(`➖ Removed track: ${track.name}`);
            return true;
        }
        return false;
    }

    /**
     * Get track statistics
     */
    getTrackStats(trackId) {
        const track = this.getTrack(trackId);
        if (!track) return null;

        const config = track.config;
        return {
            name: track.name,
            type: track.type,
            difficulty: track.difficulty,
            length: config.generation?.layout?.totalLength || 0,
            width: config.generation?.layout?.width || 0,
            hasElevation: config.generation?.layout?.elevation?.enabled || false,
            hasBanking: config.generation?.layout?.banking?.enabled || false,
            hasBarriers: config.barriers?.enabled || false,
            hasCheckpoints: config.checkpoints?.enabled || false,
            environmentType: config.environment?.terrain?.type || 'unknown'
        };
    }

    /**
     * Search tracks by criteria
     */
    searchTracks(criteria = {}) {
        const tracks = this.getAvailableTracks();
        
        return tracks.filter(track => {
            // Filter by type
            if (criteria.type && track.type !== criteria.type) {
                return false;
            }
            
            // Filter by difficulty
            if (criteria.difficulty && track.difficulty !== criteria.difficulty) {
                return false;
            }
            
            // Filter by name (partial match)
            if (criteria.name && !track.name.toLowerCase().includes(criteria.name.toLowerCase())) {
                return false;
            }
            
            return true;
        });
    }

    /**
     * Get next track in sequence
     */
    getNextTrack() {
        const tracks = this.getAvailableTracks();
        const currentIndex = tracks.findIndex(track => track.id === this.currentTrackId);
        const nextIndex = (currentIndex + 1) % tracks.length;
        return tracks[nextIndex];
    }

    /**
     * Get previous track in sequence
     */
    getPreviousTrack() {
        const tracks = this.getAvailableTracks();
        const currentIndex = tracks.findIndex(track => track.id === this.currentTrackId);
        const prevIndex = currentIndex === 0 ? tracks.length - 1 : currentIndex - 1;
        return tracks[prevIndex];
    }

    /**
     * Cycle to next track
     */
    selectNextTrack() {
        const nextTrack = this.getNextTrack();
        return this.setCurrentTrack(nextTrack.id);
    }

    /**
     * Cycle to previous track
     */
    selectPreviousTrack() {
        const prevTrack = this.getPreviousTrack();
        return this.setCurrentTrack(prevTrack.id);
    }
}
