/**
 * Input system for handling keyboard, gamepad, and touch controls
 * Provides smooth input processing for racing controls
 */
export class InputSystem {
    constructor() {
        // Input state
        this.keys = {};
        this.gamepad = null;
        this.gamepadIndex = -1;
        
        // Processed input values
        this.inputState = {
            throttle: 0,    // -1 to 1 (reverse to forward)
            steering: 0,    // -1 to 1 (left to right)
            brake: 0,       // 0 to 1
            handbrake: false,
            boost: false,
            reset: false,
            camera: false
        };
        
        // Input smoothing
        this.smoothing = {
            throttle: 0.3,  // Increased for more responsive acceleration
            steering: 0.25, // Increased for more responsive steering
            brake: 0.4      // Increased for more responsive braking
        };
        
        // Previous frame values for smoothing
        this.previousInput = { ...this.inputState };
        
        // Key mappings
        this.keyMap = {
            // Movement
            'KeyW': 'throttleForward',
            'KeyS': 'throttleReverse',
            'KeyA': 'steerLeft',
            'KeyD': 'steerRight',
            'ArrowUp': 'throttleForward',
            'ArrowDown': 'throttleReverse',
            'ArrowLeft': 'steerLeft',
            'ArrowRight': 'steerRight',
            
            // Actions
            'Space': 'brake',
            'ShiftLeft': 'boost',
            'ShiftRight': 'boost',
            'KeyX': 'handbrake',
            
            // Controls
            'KeyR': 'reset',
            'KeyC': 'camera'
        };
        
        // Gamepad settings
        this.gamepadSettings = {
            deadzone: 0.1,
            sensitivity: 1.0,
            throttleAxis: 1,    // Right stick Y or triggers
            steeringAxis: 0,    // Left stick X
            brakeButton: 6,     // RT/R2
            boostButton: 5,     // RB/R1
            handbrakeButton: 4, // LB/L1
            resetButton: 9,     // Start/Options
            cameraButton: 8     // Select/Share
        };
        
        // Bind methods
        this.onKeyDown = this.onKeyDown.bind(this);
        this.onKeyUp = this.onKeyUp.bind(this);
        this.onGamepadConnected = this.onGamepadConnected.bind(this);
        this.onGamepadDisconnected = this.onGamepadDisconnected.bind(this);
    }

    /**
     * Initialize input system
     */
    init() {
        // Set up keyboard listeners
        window.addEventListener('keydown', this.onKeyDown);
        window.addEventListener('keyup', this.onKeyUp);
        
        // Set up gamepad listeners
        window.addEventListener('gamepadconnected', this.onGamepadConnected);
        window.addEventListener('gamepaddisconnected', this.onGamepadDisconnected);
        
        // Check for already connected gamepads
        this.checkGamepads();
        
        console.log('🎮 Input system initialized');
    }

    /**
     * Handle key down events
     */
    onKeyDown(event) {
        const action = this.keyMap[event.code];
        if (action) {
            this.keys[action] = true;
            
            // Prevent default for game keys
            event.preventDefault();
        }
    }

    /**
     * Handle key up events
     */
    onKeyUp(event) {
        const action = this.keyMap[event.code];
        if (action) {
            this.keys[action] = false;
            
            // Prevent default for game keys
            event.preventDefault();
        }
    }

    /**
     * Handle gamepad connected
     */
    onGamepadConnected(event) {
        console.log('🎮 Gamepad connected:', event.gamepad.id);
        this.gamepadIndex = event.gamepad.index;
        this.updateGamepad();
    }

    /**
     * Handle gamepad disconnected
     */
    onGamepadDisconnected(event) {
        console.log('🎮 Gamepad disconnected:', event.gamepad.id);
        if (this.gamepadIndex === event.gamepad.index) {
            this.gamepadIndex = -1;
            this.gamepad = null;
        }
    }

    /**
     * Check for connected gamepads
     */
    checkGamepads() {
        const gamepads = navigator.getGamepads();
        for (let i = 0; i < gamepads.length; i++) {
            if (gamepads[i]) {
                this.gamepadIndex = i;
                this.updateGamepad();
                break;
            }
        }
    }

    /**
     * Update gamepad state
     */
    updateGamepad() {
        if (this.gamepadIndex >= 0) {
            const gamepads = navigator.getGamepads();
            this.gamepad = gamepads[this.gamepadIndex];
        }
    }

    /**
     * Process keyboard input
     */
    processKeyboardInput() {
        const rawInput = {
            throttle: 0,
            steering: 0,
            brake: 0,
            handbrake: false,
            boost: false,
            reset: false,
            camera: false
        };
        
        // Throttle input
        if (this.keys.throttleForward) rawInput.throttle += 1;
        if (this.keys.throttleReverse) rawInput.throttle -= 1;
        
        // Steering input
        if (this.keys.steerLeft) rawInput.steering -= 1;
        if (this.keys.steerRight) rawInput.steering += 1;
        
        // Brake input
        if (this.keys.brake) rawInput.brake = 1;
        
        // Action inputs
        rawInput.handbrake = this.keys.handbrake || false;
        rawInput.boost = this.keys.boost || false;
        rawInput.reset = this.keys.reset || false;
        rawInput.camera = this.keys.camera || false;
        
        return rawInput;
    }

    /**
     * Process gamepad input
     */
    processGamepadInput() {
        const rawInput = {
            throttle: 0,
            steering: 0,
            brake: 0,
            handbrake: false,
            boost: false,
            reset: false,
            camera: false
        };
        
        if (!this.gamepad) return rawInput;
        
        const settings = this.gamepadSettings;
        
        // Apply deadzone to axis
        const applyDeadzone = (value) => {
            return Math.abs(value) > settings.deadzone ? value : 0;
        };
        
        // Steering (left stick X-axis)
        if (this.gamepad.axes[settings.steeringAxis] !== undefined) {
            rawInput.steering = applyDeadzone(this.gamepad.axes[settings.steeringAxis]) * settings.sensitivity;
        }
        
        // Throttle/Brake (triggers or right stick)
        // Check if we have trigger axes (modern gamepads)
        if (this.gamepad.axes.length > 2) {
            // Use triggers: RT for throttle, LT for brake
            const rightTrigger = this.gamepad.axes[5] !== undefined ? (this.gamepad.axes[5] + 1) / 2 : 0;
            const leftTrigger = this.gamepad.axes[4] !== undefined ? (this.gamepad.axes[4] + 1) / 2 : 0;
            
            rawInput.throttle = rightTrigger;
            rawInput.brake = leftTrigger;
        } else {
            // Fallback to buttons
            if (this.gamepad.buttons[settings.brakeButton]?.pressed) rawInput.throttle = 1;
            if (this.gamepad.buttons[7]?.pressed) rawInput.brake = 1; // Fallback brake button
        }
        
        // Action buttons
        rawInput.boost = this.gamepad.buttons[settings.boostButton]?.pressed || false;
        rawInput.handbrake = this.gamepad.buttons[settings.handbrakeButton]?.pressed || false;
        rawInput.reset = this.gamepad.buttons[settings.resetButton]?.pressed || false;
        rawInput.camera = this.gamepad.buttons[settings.cameraButton]?.pressed || false;
        
        return rawInput;
    }

    /**
     * Apply input smoothing
     */
    applySmoothingToInput(rawInput) {
        const smoothed = { ...rawInput };
        
        // Apply smoothing to analog inputs
        smoothed.throttle = this.lerp(
            this.previousInput.throttle,
            rawInput.throttle,
            this.smoothing.throttle
        );
        
        smoothed.steering = this.lerp(
            this.previousInput.steering,
            rawInput.steering,
            this.smoothing.steering
        );
        
        smoothed.brake = this.lerp(
            this.previousInput.brake,
            rawInput.brake,
            this.smoothing.brake
        );
        
        return smoothed;
    }

    /**
     * Linear interpolation helper
     */
    lerp(a, b, t) {
        return a + (b - a) * t;
    }

    /**
     * Get current input state
     */
    getState() {
        // Update gamepad state
        this.updateGamepad();
        
        // Process keyboard input
        const keyboardInput = this.processKeyboardInput();
        
        // Process gamepad input
        const gamepadInput = this.processGamepadInput();
        
        // Combine inputs (gamepad takes priority for analog values)
        const combinedInput = {
            throttle: gamepadInput.throttle || keyboardInput.throttle,
            steering: gamepadInput.steering || keyboardInput.steering,
            brake: gamepadInput.brake || keyboardInput.brake,
            handbrake: gamepadInput.handbrake || keyboardInput.handbrake,
            boost: gamepadInput.boost || keyboardInput.boost,
            reset: gamepadInput.reset || keyboardInput.reset,
            camera: gamepadInput.camera || keyboardInput.camera
        };
        
        // Apply smoothing
        this.inputState = this.applySmoothingToInput(combinedInput);
        
        // Store for next frame
        this.previousInput = { ...this.inputState };
        
        return this.inputState;
    }

    /**
     * Check if a specific action is pressed
     */
    isPressed(action) {
        return this.inputState[action] || false;
    }

    /**
     * Get raw input value for an action
     */
    getValue(action) {
        return this.inputState[action] || 0;
    }

    /**
     * Set input smoothing values
     */
    setSmoothingValues(throttle, steering, brake) {
        this.smoothing.throttle = throttle;
        this.smoothing.steering = steering;
        this.smoothing.brake = brake;
    }

    /**
     * Enable/disable input processing
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        if (!enabled) {
            // Reset input state when disabled
            this.inputState = {
                throttle: 0,
                steering: 0,
                brake: 0,
                handbrake: false,
                boost: false,
                reset: false,
                camera: false
            };
        }
    }

    /**
     * Cleanup input system
     */
    destroy() {
        window.removeEventListener('keydown', this.onKeyDown);
        window.removeEventListener('keyup', this.onKeyUp);
        window.removeEventListener('gamepadconnected', this.onGamepadConnected);
        window.removeEventListener('gamepaddisconnected', this.onGamepadDisconnected);
    }
}
