import * as THREE from 'three';

/**
 * Audio system for racing game sounds
 * Handles engine sounds, music, and sound effects
 */
export class AudioSystem {
    constructor() {
        this.audioContext = null;
        this.masterGain = null;
        this.sounds = {};
        this.music = {};
        
        // Audio settings
        this.settings = {
            masterVolume: 0.7,
            musicVolume: 0.5,
            sfxVolume: 0.8,
            engineVolume: 0.6,
            enabled: true
        };
        
        // Engine sound synthesis
        this.engineSound = {
            oscillator: null,
            gainNode: null,
            filterNode: null,
            isPlaying: false,
            baseFrequency: 80,
            currentFrequency: 80
        };
        
        // Sound effects
        this.sfx = {
            boost: null,
            brake: null,
            collision: null,
            checkpoint: null,
            lapComplete: null
        };
        
        // Music tracks
        this.musicTracks = [];
        this.currentTrack = null;
        this.isPlayingMusic = false;
    }

    /**
     * Initialize audio system
     */
    async init() {
        try {
            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create master gain node
            this.masterGain = this.audioContext.createGain();
            this.masterGain.gain.value = this.settings.masterVolume;
            this.masterGain.connect(this.audioContext.destination);
            
            // Initialize engine sound
            this.initEngineSound();
            
            // Load sound effects
            await this.loadSoundEffects();
            
            // Set up user interaction handler for audio context
            this.setupUserInteractionHandler();
            
            console.log('🔊 Audio system initialized');
            
        } catch (error) {
            console.warn('⚠️ Audio system failed to initialize:', error);
            this.settings.enabled = false;
        }
    }

    /**
     * Initialize engine sound synthesis
     */
    initEngineSound() {
        if (!this.audioContext) return;
        
        // Create oscillator for engine base sound
        this.engineSound.oscillator = this.audioContext.createOscillator();
        this.engineSound.oscillator.type = 'sawtooth';
        this.engineSound.oscillator.frequency.value = this.engineSound.baseFrequency;
        
        // Create filter for engine character
        this.engineSound.filterNode = this.audioContext.createBiquadFilter();
        this.engineSound.filterNode.type = 'lowpass';
        this.engineSound.filterNode.frequency.value = 800;
        this.engineSound.filterNode.Q.value = 1;
        
        // Create gain node for engine volume
        this.engineSound.gainNode = this.audioContext.createGain();
        this.engineSound.gainNode.gain.value = 0;
        
        // Connect audio nodes
        this.engineSound.oscillator.connect(this.engineSound.filterNode);
        this.engineSound.filterNode.connect(this.engineSound.gainNode);
        this.engineSound.gainNode.connect(this.masterGain);
        
        // Start oscillator
        this.engineSound.oscillator.start();
        this.engineSound.isPlaying = true;
    }

    /**
     * Load sound effects
     */
    async loadSoundEffects() {
        // For now, create placeholder sound effects using synthesis
        // In a real implementation, you would load audio files
        
        this.createSynthesizedSounds();
    }

    /**
     * Create synthesized sound effects
     */
    createSynthesizedSounds() {
        // Boost sound effect
        this.sfx.boost = this.createSynthSound({
            type: 'sine',
            frequency: 440,
            duration: 0.5,
            volume: 0.3,
            fadeOut: true
        });
        
        // Brake sound effect
        this.sfx.brake = this.createSynthSound({
            type: 'noise',
            frequency: 200,
            duration: 0.3,
            volume: 0.2,
            fadeOut: true
        });
        
        // Collision sound effect
        this.sfx.collision = this.createSynthSound({
            type: 'square',
            frequency: 150,
            duration: 0.8,
            volume: 0.4,
            fadeOut: true
        });
    }

    /**
     * Create a synthesized sound
     */
    createSynthSound(config) {
        return {
            play: () => {
                if (!this.audioContext || !this.settings.enabled) return;
                
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                
                oscillator.type = config.type === 'noise' ? 'white' : config.type;
                oscillator.frequency.value = config.frequency;
                
                gainNode.gain.value = config.volume * this.settings.sfxVolume;
                
                oscillator.connect(gainNode);
                gainNode.connect(this.masterGain);
                
                const now = this.audioContext.currentTime;
                oscillator.start(now);
                
                if (config.fadeOut) {
                    gainNode.gain.exponentialRampToValueAtTime(0.001, now + config.duration);
                }
                
                oscillator.stop(now + config.duration);
            }
        };
    }

    /**
     * Set up user interaction handler for audio context
     */
    setupUserInteractionHandler() {
        const resumeAudio = () => {
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
        };
        
        // Resume audio on first user interaction
        document.addEventListener('click', resumeAudio, { once: true });
        document.addEventListener('keydown', resumeAudio, { once: true });
        document.addEventListener('touchstart', resumeAudio, { once: true });
    }

    /**
     * Update audio system
     */
    update(deltaTime, carData) {
        if (!this.settings.enabled || !this.audioContext) return;
        
        // Update engine sound
        this.updateEngineSound(carData);
        
        // Update 3D audio positioning if needed
        this.update3DAudio(carData);
    }

    /**
     * Update engine sound based on car data
     */
    updateEngineSound(carData) {
        if (!this.engineSound.isPlaying || !carData) return;
        
        const { speed, rpm, throttle, boost } = carData;
        
        // Calculate target frequency based on RPM
        const rpmFactor = (rpm - 1000) / 7000; // Normalize RPM
        const targetFrequency = this.engineSound.baseFrequency + (rpmFactor * 300);
        
        // Smooth frequency transition
        this.engineSound.currentFrequency = THREE.MathUtils.lerp(
            this.engineSound.currentFrequency,
            targetFrequency,
            0.1
        );
        
        // Update oscillator frequency
        this.engineSound.oscillator.frequency.setValueAtTime(
            this.engineSound.currentFrequency,
            this.audioContext.currentTime
        );
        
        // Update filter frequency based on throttle
        const filterFreq = 400 + (Math.abs(throttle) * 800);
        this.engineSound.filterNode.frequency.setValueAtTime(
            filterFreq,
            this.audioContext.currentTime
        );
        
        // Update volume based on throttle and speed
        const baseVolume = 0.1 + (Math.abs(throttle) * 0.4);
        const speedVolume = Math.min(speed / 200, 0.3); // Max additional volume at 200 km/h
        const boostVolume = boost ? 0.2 : 0;
        
        const targetVolume = (baseVolume + speedVolume + boostVolume) * this.settings.engineVolume;
        
        this.engineSound.gainNode.gain.setValueAtTime(
            targetVolume,
            this.audioContext.currentTime
        );
    }

    /**
     * Update 3D audio positioning
     */
    update3DAudio(carData) {
        // Placeholder for 3D audio positioning
        // Would use Web Audio API's PannerNode for spatial audio
    }

    /**
     * Play sound effect
     */
    playSound(soundName, volume = 1.0) {
        if (!this.settings.enabled || !this.sfx[soundName]) return;
        
        this.sfx[soundName].play();
    }

    /**
     * Play boost sound
     */
    playBoost() {
        this.playSound('boost');
    }

    /**
     * Play brake sound
     */
    playBrake() {
        this.playSound('brake');
    }

    /**
     * Play collision sound
     */
    playCollision() {
        this.playSound('collision');
    }

    /**
     * Set master volume
     */
    setMasterVolume(volume) {
        this.settings.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.masterGain.gain.value = this.settings.masterVolume;
        }
    }

    /**
     * Set music volume
     */
    setMusicVolume(volume) {
        this.settings.musicVolume = Math.max(0, Math.min(1, volume));
        // Update current music volume if playing
    }

    /**
     * Set SFX volume
     */
    setSFXVolume(volume) {
        this.settings.sfxVolume = Math.max(0, Math.min(1, volume));
    }

    /**
     * Set engine volume
     */
    setEngineVolume(volume) {
        this.settings.engineVolume = Math.max(0, Math.min(1, volume));
    }

    /**
     * Enable/disable audio
     */
    setEnabled(enabled) {
        this.settings.enabled = enabled;
        
        if (!enabled && this.engineSound.gainNode) {
            this.engineSound.gainNode.gain.value = 0;
        }
    }

    /**
     * Get audio settings
     */
    getSettings() {
        return { ...this.settings };
    }

    /**
     * Mute all audio
     */
    mute() {
        if (this.masterGain) {
            this.masterGain.gain.value = 0;
        }
    }

    /**
     * Unmute audio
     */
    unmute() {
        if (this.masterGain) {
            this.masterGain.gain.value = this.settings.masterVolume;
        }
    }

    /**
     * Cleanup audio system
     */
    destroy() {
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        
        this.sounds = {};
        this.music = {};
        this.sfx = {};
    }
}
