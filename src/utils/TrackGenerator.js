import * as THREE from 'three';
import { MathUtils } from './MathUtils.js';

/**
 * Procedural track generator for racing circuits
 * Creates Wipeout-style futuristic racing tracks
 */
export class TrackGenerator {
    constructor(config) {
        this.config = config;
        this.random = new MathUtils.SeededRandom(config.generation.seed);
    }

    /**
     * Generate complete track data
     */
    async generate() {
        console.log('🏗️ Generating track layout...');

        // Handle playground tracks differently
        if (this.config.type === 'playground') {
            return this.generatePlaygroundData();
        }

        // Generate base track path
        const trackPath = this.generateTrackPath();

        // Apply elevation changes
        const elevatedPath = this.applyElevation(trackPath);

        // Apply banking to corners
        const bankedPath = this.applyBanking(elevatedPath);

        // Create track segments
        const segments = this.createTrackSegments(bankedPath);

        // Calculate track length
        const length = this.calculateTrackLength(segments);

        return {
            points: segments,
            segments: segments,
            spline: this.createSpline(segments),
            length: length,
            width: this.config.generation.layout.width
        };
    }

    /**
     * Generate playground track data (simple flat area)
     */
    generatePlaygroundData() {
        const size = this.config.generation.terrain.size || 500;

        // Create a simple single point at origin for playground
        const centerPoint = {
            position: new THREE.Vector3(0, 0, 0),
            direction: new THREE.Vector3(1, 0, 0),
            banking: 0,
            elevation: 0,
            curvature: 0
        };

        return {
            points: [centerPoint],
            segments: [centerPoint],
            spline: null,
            length: 0,
            width: size
        };
    }

    /**
     * Generate base track path using curves and straights
     */
    generateTrackPath() {
        const layout = this.config.generation.layout;
        const points = [];
        const segmentCount = layout.segments;
        
        // Start at origin
        let currentPos = new THREE.Vector3(0, 0, 0);
        let currentAngle = 0;
        
        for (let i = 0; i < segmentCount; i++) {
            const t = i / segmentCount;
            
            // Determine if this should be a curve or straight
            const isCurve = this.shouldCreateCurve(t);
            
            if (isCurve) {
                // Create curved section
                const curveData = this.generateCurveSection(currentPos, currentAngle, t);
                points.push(...curveData.points);
                currentPos = curveData.endPos;
                currentAngle = curveData.endAngle;
            } else {
                // Create straight section
                const straightData = this.generateStraightSection(currentPos, currentAngle);
                points.push(...straightData.points);
                currentPos = straightData.endPos;
                currentAngle = straightData.endAngle;
            }
        }
        
        // Close the loop if needed
        if (layout.closed) {
            this.closeTrackLoop(points);
        }
        
        return points;
    }

    /**
     * Determine if a curve should be created at this position
     */
    shouldCreateCurve(t) {
        const curveConfig = this.config.generation.curves;
        const straightConfig = this.config.generation.straights;
        
        // Use noise function for natural curve distribution
        const noise = this.random.noise(t * 10);
        return noise > (1 - curveConfig.frequency);
    }

    /**
     * Generate a curved track section
     */
    generateCurveSection(startPos, startAngle, t) {
        const curveConfig = this.config.generation.curves;
        const points = [];
        
        // Determine curve parameters
        const radius = this.random.range(curveConfig.minRadius, curveConfig.maxRadius);
        const angleChange = this.random.range(-Math.PI / 2, Math.PI / 2) * curveConfig.sharpness;
        const segmentCount = Math.max(5, Math.floor(Math.abs(angleChange) * radius / 10));
        
        // Calculate curve center
        const centerOffset = new THREE.Vector3(
            Math.cos(startAngle + Math.PI / 2) * radius,
            0,
            Math.sin(startAngle + Math.PI / 2) * radius
        );
        const center = startPos.clone().add(centerOffset);
        
        // Generate curve points
        for (let i = 0; i <= segmentCount; i++) {
            const curveT = i / segmentCount;
            const angle = startAngle + angleChange * curveT;
            
            const point = new THREE.Vector3(
                center.x + Math.cos(angle + Math.PI / 2) * radius,
                0,
                center.z + Math.sin(angle + Math.PI / 2) * radius
            );
            
            points.push({
                position: point,
                angle: angle,
                curvature: Math.abs(angleChange) / radius,
                type: 'curve'
            });
        }
        
        return {
            points: points,
            endPos: points[points.length - 1].position,
            endAngle: startAngle + angleChange
        };
    }

    /**
     * Generate a straight track section
     */
    generateStraightSection(startPos, startAngle) {
        const straightConfig = this.config.generation.straights;
        const points = [];
        
        // Determine straight length
        const length = this.random.range(straightConfig.minLength, straightConfig.maxLength);
        const segmentCount = Math.floor(length / 10); // 10m per segment
        
        // Generate straight points
        for (let i = 0; i <= segmentCount; i++) {
            const distance = (i / segmentCount) * length;
            
            const point = new THREE.Vector3(
                startPos.x + Math.cos(startAngle) * distance,
                0,
                startPos.z + Math.sin(startAngle) * distance
            );
            
            points.push({
                position: point,
                angle: startAngle,
                curvature: 0,
                type: 'straight'
            });
        }
        
        return {
            points: points,
            endPos: points[points.length - 1].position,
            endAngle: startAngle
        };
    }

    /**
     * Close the track loop
     */
    closeTrackLoop(points) {
        if (points.length < 3) return;
        
        const firstPoint = points[0];
        const lastPoint = points[points.length - 1];
        
        // Calculate connection curve
        const distance = firstPoint.position.distanceTo(lastPoint.position);
        const connectionSegments = Math.max(3, Math.floor(distance / 10));
        
        // Create smooth connection
        for (let i = 1; i <= connectionSegments; i++) {
            const t = i / (connectionSegments + 1);
            const pos = lastPoint.position.clone().lerp(firstPoint.position, t);
            
            points.push({
                position: pos,
                angle: THREE.MathUtils.lerp(lastPoint.angle, firstPoint.angle, t),
                curvature: 0.1,
                type: 'connection'
            });
        }
    }

    /**
     * Apply elevation changes to track
     */
    applyElevation(trackPath) {
        const elevationConfig = this.config.generation.layout.elevation;
        
        if (!elevationConfig.enabled) return trackPath;
        
        return trackPath.map((point, index) => {
            const t = index / trackPath.length;
            
            // Generate elevation using multiple noise octaves
            let elevation = 0;
            elevation += this.random.noise(t * 2) * elevationConfig.maxHeight * 0.5;
            elevation += this.random.noise(t * 4) * elevationConfig.maxHeight * 0.3;
            elevation += this.random.noise(t * 8) * elevationConfig.maxHeight * 0.2;
            
            // Apply smoothing
            if (index > 0 && index < trackPath.length - 1) {
                const prevElevation = trackPath[index - 1].position.y;
                const nextElevation = elevation;
                elevation = THREE.MathUtils.lerp(prevElevation, nextElevation, elevationConfig.smoothness);
            }
            
            return {
                ...point,
                position: new THREE.Vector3(point.position.x, elevation, point.position.z)
            };
        });
    }

    /**
     * Apply banking to corners
     */
    applyBanking(trackPath) {
        const bankingConfig = this.config.generation.layout.banking;
        
        if (!bankingConfig.enabled) return trackPath;
        
        return trackPath.map((point, index) => {
            // Calculate banking based on curvature
            const banking = point.curvature * bankingConfig.maxAngle * bankingConfig.cornerMultiplier;
            
            // Apply smoothing
            let smoothedBanking = banking;
            if (index > 0 && index < trackPath.length - 1) {
                const prevBanking = trackPath[index - 1].banking || 0;
                smoothedBanking = THREE.MathUtils.lerp(prevBanking, banking, bankingConfig.smoothness);
            }
            
            return {
                ...point,
                banking: smoothedBanking
            };
        });
    }

    /**
     * Create track segments with additional data
     */
    createTrackSegments(trackPath) {
        return trackPath.map((point, index) => {
            const nextIndex = (index + 1) % trackPath.length;
            const nextPoint = trackPath[nextIndex];
            
            // Calculate segment direction
            const direction = new THREE.Vector3()
                .subVectors(nextPoint.position, point.position)
                .normalize();
            
            // Calculate right vector for banking
            const right = new THREE.Vector3()
                .crossVectors(direction, new THREE.Vector3(0, 1, 0))
                .normalize();
            
            // Apply banking rotation
            if (point.banking) {
                const bankingRotation = new THREE.Matrix4()
                    .makeRotationAxis(direction, point.banking * Math.PI / 180);
                right.applyMatrix4(bankingRotation);
            }
            
            return {
                ...point,
                direction: direction,
                right: right,
                normal: new THREE.Vector3().crossVectors(right, direction),
                segmentIndex: index
            };
        });
    }

    /**
     * Create spline for smooth interpolation
     */
    createSpline(segments) {
        const points = segments.map(segment => segment.position);
        return new THREE.CatmullRomCurve3(points, this.config.generation.layout.closed);
    }

    /**
     * Calculate total track length
     */
    calculateTrackLength(segments) {
        let totalLength = 0;
        
        for (let i = 0; i < segments.length - 1; i++) {
            const current = segments[i];
            const next = segments[i + 1];
            totalLength += current.position.distanceTo(next.position);
        }
        
        // Add closing segment if track is closed
        if (this.config.generation.layout.closed && segments.length > 0) {
            totalLength += segments[segments.length - 1].position.distanceTo(segments[0].position);
        }
        
        return totalLength;
    }

    /**
     * Get track point at specific distance
     */
    getPointAtDistance(segments, distance) {
        const spline = this.createSpline(segments);
        const t = distance / this.calculateTrackLength(segments);
        return spline.getPoint(t % 1);
    }

    /**
     * Get track direction at specific distance
     */
    getDirectionAtDistance(segments, distance) {
        const spline = this.createSpline(segments);
        const t = distance / this.calculateTrackLength(segments);
        return spline.getTangent(t % 1);
    }
}
