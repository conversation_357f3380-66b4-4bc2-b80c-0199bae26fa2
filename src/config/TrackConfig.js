/**
 * Track configuration for procedural track generation
 * Designed for Wipeout-style fast racing tracks
 */
export const TrackConfig = {
    // Track generation parameters
    generation: {
        type: 'procedural', // 'procedural' or 'predefined'
        seed: 12345,        // Random seed for consistent generation
        
        // Track layout
        layout: {
            totalLength: 2000,    // Total track length in meters
            width: 12,            // Track width in meters
            segments: 100,        // Number of track segments
            closed: true,         // Whether track forms a loop
            
            // Elevation changes
            elevation: {
                enabled: false,     // Disabled to prevent vertical walls
                maxHeight: 5,       // Reduced maximum height difference
                frequency: 0.02,    // How often elevation changes
                smoothness: 0.9     // Increased smoothness
            },
            
            // Banking (track tilting in corners)
            banking: {
                enabled: false,     // Disabled to prevent track tilting issues
                maxAngle: 10,       // Reduced maximum banking angle
                cornerMultiplier: 1.0, // Reduced banking multiplier
                smoothness: 0.95    // Increased smoothness
            }
        },
        
        // Curve generation
        curves: {
            frequency: 0.05,      // How often curves occur
            maxRadius: 200,       // Maximum curve radius
            minRadius: 50,        // Minimum curve radius
            sharpness: 0.7,       // How sharp curves can be
            variation: 0.3        // Random variation in curve parameters
        },
        
        // Straight sections
        straights: {
            minLength: 100,       // Minimum straight section length
            maxLength: 300,       // Maximum straight section length
            frequency: 0.3        // Frequency of long straights
        }
    },
    
    // Track surface properties
    surface: {
        material: 'racing',     // Surface type
        grip: 1.0,             // Base grip multiplier
        friction: 0.8,         // Friction coefficient
        
        // Surface variations
        variations: {
            enabled: true,
            boostPads: {
                enabled: true,
                frequency: 0.1,    // How often boost pads appear
                length: 20,        // Length of boost pads
                speedMultiplier: 1.5 // Speed boost multiplier
            },
            
            roughSections: {
                enabled: true,
                frequency: 0.05,   // How often rough sections appear
                length: 30,        // Length of rough sections
                gripReduction: 0.7 // Grip reduction in rough sections
            }
        }
    },
    
    // Track barriers and boundaries
    barriers: {
        enabled: true,
        height: 2.0,           // Barrier height
        thickness: 0.5,        // Barrier thickness
        material: 'energy',    // Barrier type ('solid', 'energy', 'magnetic')
        
        // Barrier properties
        properties: {
            restitution: 0.6,   // Bounce factor
            friction: 0.3,      // Barrier friction
            damage: false       // Whether barriers cause damage
        },
        
        // Visual effects
        effects: {
            glow: true,         // Energy barrier glow effect
            particles: true,    // Particle effects on collision
            color: 0x00ffff     // Barrier color
        }
    },
    
    // Checkpoints and timing
    checkpoints: {
        enabled: true,
        count: 10,             // Number of checkpoints
        width: 15,             // Checkpoint width (wider than track)
        height: 5,             // Checkpoint height
        
        // Visual appearance
        visual: {
            enabled: true,
            style: 'holographic', // 'holographic', 'solid', 'minimal'
            color: 0x00ff00,      // Checkpoint color
            animation: true       // Animated checkpoint effects
        },
        
        // Timing system
        timing: {
            tolerance: 2.0,     // Time tolerance for checkpoint detection
            required: true      // Whether all checkpoints must be hit
        }
    },
    
    // Track environment
    environment: {
        // Surrounding terrain
        terrain: {
            enabled: true,
            type: 'futuristic',  // 'futuristic', 'desert', 'city', 'space'
            detail: 'medium',    // 'low', 'medium', 'high'
            
            // Terrain generation
            generation: {
                size: 1000,      // Terrain size around track
                height: 50,      // Maximum terrain height
                roughness: 0.5   // Terrain roughness
            }
        },
        
        // Atmospheric effects
        atmosphere: {
            fog: {
                enabled: true,
                density: 0.01,   // Fog density
                color: 0x87CEEB  // Fog color
            },
            
            lighting: {
                ambient: 0.4,    // Ambient light intensity
                directional: 1.0, // Directional light intensity
                shadows: true    // Enable shadows
            },
            
            particles: {
                enabled: true,
                type: 'energy',  // 'energy', 'dust', 'sparks'
                density: 0.5     // Particle density
            }
        },
        
        // Background elements
        background: {
            skybox: 'futuristic', // Skybox type
            distantObjects: true, // Show distant buildings/mountains
            horizon: true         // Show horizon effects
        }
    },
    
    // Performance optimization
    optimization: {
        // Level of detail
        lod: {
            enabled: true,
            distances: [50, 150, 500], // LOD switch distances
            trackDetail: [1.0, 0.7, 0.3], // Track detail levels
            environmentDetail: [1.0, 0.5, 0.2] // Environment detail levels
        },
        
        // Culling
        culling: {
            enabled: true,
            distance: 300,       // Culling distance
            frustum: true        // Frustum culling
        },
        
        // Instancing for repeated objects
        instancing: {
            enabled: true,
            barriers: true,      // Instance barrier segments
            checkpoints: true,   // Instance checkpoint elements
            environment: true    // Instance environment objects
        }
    },
    
    // Debug and development
    debug: {
        enabled: false,
        showWireframe: false,   // Show track wireframe
        showCheckpoints: true,  // Highlight checkpoints
        showBarriers: true,     // Highlight barriers
        showPath: false,        // Show racing line
        showNormals: false,     // Show surface normals
        
        // Performance monitoring
        performance: {
            showStats: false,   // Show performance stats
            logGeneration: false // Log track generation process
        }
    }
};
