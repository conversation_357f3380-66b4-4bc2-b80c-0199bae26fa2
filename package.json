{"name": "webracer-threejs", "version": "1.0.0", "description": "High-speed racing game with Three.js and configurable physics", "main": "src/main.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000"}, "dependencies": {"@playwright/test": "^1.54.1", "cannon-es": "^0.20.0", "three": "^0.160.0"}, "devDependencies": {"vite": "^5.0.0"}, "keywords": ["racing", "game", "threejs", "physics", "wipeout"], "author": "", "license": "MIT"}