<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Car Movement Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        #gameContainer {
            width: 100%;
            height: 600px;
            border: 2px solid #333;
            position: relative;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
        }
        
        #instructions {
            margin-bottom: 20px;
            padding: 15px;
            background: #333;
            border-radius: 5px;
        }
        
        #debug {
            margin-top: 20px;
            padding: 15px;
            background: #333;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .key {
            background: #555;
            padding: 2px 6px;
            border-radius: 3px;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <div id="instructions">
        <h2>🏎️ Car Movement Test</h2>
        <p>Test the car movement fixes:</p>
        <ul>
            <li><span class="key">W</span> or <span class="key">↑</span> - Accelerate forward</li>
            <li><span class="key">S</span> or <span class="key">↓</span> - Reverse</li>
            <li><span class="key">A</span> or <span class="key">←</span> - Steer left</li>
            <li><span class="key">D</span> or <span class="key">→</span> - Steer right</li>
            <li><span class="key">Space</span> - Brake</li>
            <li><span class="key">Shift</span> - Boost</li>
        </ul>
        <p><strong>Expected behavior:</strong> Car should accelerate smoothly when pressing W/↑, even when touching the ground.</p>
    </div>
    
    <div id="gameContainer"></div>
    
    <div id="debug">
        <div>Loading game...</div>
    </div>

    <script type="module">
        import { Game } from './src/core/Game.js';
        import { GameConfig } from './src/config/GameConfig.js';

        let game;
        let debugElement = document.getElementById('debug');

        async function initGame() {
            try {
                // Create game instance
                game = new Game(GameConfig);
                
                // Get the game container
                const container = document.getElementById('gameContainer');
                
                // Initialize the game
                await game.init(container);
                
                // Start the game
                game.start();
                
                debugElement.innerHTML = `
                    <div>✅ Game initialized successfully!</div>
                    <div>🎮 Use WASD or arrow keys to control the car</div>
                    <div>🔧 Fixes applied:</div>
                    <div>  • Re-enabled suspension system</div>
                    <div>  • Reduced friction (0.3 → 0.2 for track, 0.8 → 0.3 for ground)</div>
                    <div>  • Increased engine force (25000 → 35000)</div>
                    <div>  • Improved hover forces (1000 → 15000)</div>
                    <div>  • Fixed forward direction (X → Z axis)</div>
                `;
                
                // Add real-time debug info
                setInterval(() => {
                    if (game && game.car) {
                        const carState = game.car.physics.getState();
                        const position = game.car.getPosition();
                        
                        debugElement.innerHTML += `
                            <div style="margin-top: 10px; border-top: 1px solid #555; padding-top: 10px;">
                                <div>Speed: ${carState.speed.toFixed(1)} km/h</div>
                                <div>Position: (${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})</div>
                                <div>Grounded: ${carState.isGrounded ? '✅' : '❌'}</div>
                                <div>Throttle: ${carState.throttle.toFixed(2)}</div>
                                <div>Boost: ${carState.boost.toFixed(0)}%</div>
                            </div>
                        `;
                        
                        // Keep only the last few debug entries
                        const debugLines = debugElement.innerHTML.split('<div style="margin-top: 10px;');
                        if (debugLines.length > 3) {
                            debugElement.innerHTML = debugLines.slice(0, 2).join('<div style="margin-top: 10px;') + 
                                                   '<div style="margin-top: 10px;' + debugLines[debugLines.length - 1];
                        }
                    }
                }, 500);
                
            } catch (error) {
                console.error('Failed to initialize game:', error);
                debugElement.innerHTML = `
                    <div>❌ Failed to initialize game:</div>
                    <div style="color: #ff6b6b;">${error.message}</div>
                    <div>Check the browser console for more details.</div>
                `;
            }
        }

        // Initialize the game when the page loads
        initGame();
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (game) {
                game.destroy();
            }
        });
    </script>
</body>
</html>
