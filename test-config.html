<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Configuration Test</h1>
    <div id="output"></div>
    
    <script type="module">
        const output = document.getElementById('output');
        
        function addOutput(message, type = 'info') {
            const p = document.createElement('p');
            p.className = type;
            p.innerHTML = message;
            output.appendChild(p);
        }
        
        async function testConfigurations() {
            try {
                addOutput('<strong>🧪 Testing Track Configurations...</strong>');
                
                // Test playground track config
                const { PlaygroundTrackConfig } = await import('./src/config/tracks/PlaygroundTrack.js');
                addOutput('✅ PlaygroundTrackConfig loaded', 'success');
                
                // Check barriers.effects.color
                if (PlaygroundTrackConfig.barriers && PlaygroundTrackConfig.barriers.effects && PlaygroundTrackConfig.barriers.effects.color !== undefined) {
                    addOutput(`✅ barriers.effects.color: ${PlaygroundTrackConfig.barriers.effects.color.toString(16)}`, 'success');
                } else {
                    addOutput('❌ barriers.effects.color is missing', 'error');
                }
                
                // Check checkpoints.visual.color
                if (PlaygroundTrackConfig.checkpoints && PlaygroundTrackConfig.checkpoints.visual && PlaygroundTrackConfig.checkpoints.visual.color !== undefined) {
                    addOutput(`✅ checkpoints.visual.color: ${PlaygroundTrackConfig.checkpoints.visual.color.toString(16)}`, 'success');
                } else {
                    addOutput('❌ checkpoints.visual.color is missing', 'error');
                }
                
                // Test original track config
                const { TrackConfig } = await import('./src/config/TrackConfig.js');
                addOutput('✅ TrackConfig loaded', 'success');
                
                // Check barriers.effects.color
                if (TrackConfig.barriers && TrackConfig.barriers.effects && TrackConfig.barriers.effects.color !== undefined) {
                    addOutput(`✅ TrackConfig barriers.effects.color: ${TrackConfig.barriers.effects.color.toString(16)}`, 'success');
                } else {
                    addOutput('❌ TrackConfig barriers.effects.color is missing', 'error');
                }
                
                // Test Track class
                addOutput('<strong>Testing Track class...</strong>');
                const { Track } = await import('./src/entities/Track.js');
                addOutput('✅ Track class loaded', 'success');
                
                // Test creating track with playground config
                const playgroundTrack = new Track(PlaygroundTrackConfig);
                addOutput('✅ Playground track instance created', 'success');
                
                // Test creating track with racing config
                const racingTrack = new Track(TrackConfig);
                addOutput('✅ Racing track instance created', 'success');
                
                addOutput('<strong>✅ All configuration tests passed!</strong>', 'success');
                addOutput('<strong>The game should now load without errors.</strong>', 'info');
                
            } catch (error) {
                console.error('Error:', error);
                addOutput(`❌ Error: ${error.message}`, 'error');
                addOutput(`Stack: ${error.stack}`, 'error');
            }
        }
        
        testConfigurations();
    </script>
</body>
</html>
