<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>WebRacer Complete Functionality Test</h1>
    <div id="output"></div>
    
    <script type="module">
        const output = document.getElementById('output');
        
        function addOutput(message, type = 'info') {
            const p = document.createElement('p');
            p.className = type;
            p.innerHTML = message;
            output.appendChild(p);
        }
        
        async function runTests() {
            try {
                addOutput('<strong>🧪 Starting Complete Functionality Tests...</strong>');
                
                // Test 1: Basic Dependencies
                addOutput('<div class="test-section"><strong>Test 1: Dependencies</strong></div>');
                
                const CANNON = await import('cannon-es');
                addOutput('✅ CANNON.js loaded', 'success');
                
                const THREE = await import('three');
                addOutput('✅ THREE.js loaded', 'success');
                
                // Test 2: Core Systems
                addOutput('<div class="test-section"><strong>Test 2: Core Systems</strong></div>');
                
                const { PhysicsWorld } = await import('./src/physics/PhysicsWorld.js');
                addOutput('✅ PhysicsWorld loaded', 'success');
                
                const { TrackSelection } = await import('./src/systems/TrackSelection.js');
                addOutput('✅ TrackSelection loaded', 'success');
                
                // Test 3: Track Configurations
                addOutput('<div class="test-section"><strong>Test 3: Track Configurations</strong></div>');
                
                const { PlaygroundTrackConfig } = await import('./src/config/tracks/PlaygroundTrack.js');
                addOutput('✅ PlaygroundTrackConfig loaded', 'success');
                
                const { TrackConfig } = await import('./src/config/TrackConfig.js');
                addOutput('✅ TrackConfig loaded', 'success');
                
                // Test 4: Physics System
                addOutput('<div class="test-section"><strong>Test 4: Physics System</strong></div>');
                
                const physicsConfig = {
                    gravity: -9.82,
                    broadphase: 'naive',
                    solver: { iterations: 10, tolerance: 1e-4 },
                    timeStep: 1/60,
                    maxSubSteps: 3
                };
                
                const physics = new PhysicsWorld(physicsConfig);
                physics.init();
                addOutput('✅ PhysicsWorld initialized', 'success');
                
                const plane = physics.createPlane();
                addOutput('✅ createPlane method works (car movement fix)', 'success');
                
                // Test 5: Track Selection System
                addOutput('<div class="test-section"><strong>Test 5: Track Selection System</strong></div>');
                
                const trackSelection = new TrackSelection();
                addOutput('✅ TrackSelection instantiated', 'success');
                
                const tracks = trackSelection.getAvailableTracks();
                addOutput(`✅ Found ${tracks.length} available tracks`, 'success');
                
                tracks.forEach(track => {
                    addOutput(`&nbsp;&nbsp;- ${track.name}: ${track.description}`, 'info');
                });
                
                // Test 6: Track Switching
                addOutput('<div class="test-section"><strong>Test 6: Track Switching</strong></div>');
                
                const initialTrack = trackSelection.getCurrentTrack();
                addOutput(`Current track: ${initialTrack.name}`, 'info');
                
                trackSelection.setCurrentTrack('racing');
                const racingTrack = trackSelection.getCurrentTrack();
                addOutput(`✅ Switched to: ${racingTrack.name}`, 'success');
                
                trackSelection.setCurrentTrack('playground');
                const playgroundTrack = trackSelection.getCurrentTrack();
                addOutput(`✅ Switched to: ${playgroundTrack.name}`, 'success');
                
                // Test 7: Track Configurations
                addOutput('<div class="test-section"><strong>Test 7: Track Configuration Details</strong></div>');
                
                const playgroundConfig = trackSelection.getTrack('playground').config;
                addOutput(`Playground size: ${playgroundConfig.generation.terrain.size}x${playgroundConfig.generation.terrain.size}m`, 'info');
                addOutput(`Playground spawn: (${playgroundConfig.spawn.position.x}, ${playgroundConfig.spawn.position.y}, ${playgroundConfig.spawn.position.z})`, 'info');
                addOutput(`Grid enabled: ${playgroundConfig.environment.grid.enabled}`, 'info');
                
                // Test 8: Game Integration
                addOutput('<div class="test-section"><strong>Test 8: Game Integration</strong></div>');
                
                const { Game } = await import('./src/core/Game.js');
                addOutput('✅ Game class loaded', 'success');
                
                const { GameConfig } = await import('./src/config/GameConfig.js');
                addOutput('✅ GameConfig loaded', 'success');
                
                addOutput('<div class="test-section"><strong>🎉 All Tests Completed Successfully!</strong></div>', 'success');
                
                addOutput('<div class="test-section"><strong>🎮 How to Test the Game:</strong><br>' +
                         '1. Open the main page (index.html)<br>' +
                         '2. Wait for the game to load<br>' +
                         '3. Use WASD or arrow keys to move the car<br>' +
                         '4. Press T to switch tracks<br>' +
                         '5. Press 1 for playground, 2 for racing track<br>' +
                         '6. Press R to reset car position</div>', 'info');
                
            } catch (error) {
                console.error('Error:', error);
                addOutput(`❌ Error: ${error.message}`, 'error');
                addOutput(`Stack: ${error.stack}`, 'error');
            }
        }
        
        runTests();
    </script>
</body>
</html>
